import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/language_provider.dart';
import '../../utils/app_localizations.dart';

import '../../widgets/common/enhanced_widgets.dart';

import 'create_request_screen.dart';
import 'my_orders_page.dart';
import 'services_screen.dart';
import '../chat/chat_list_screen.dart';

class ClientDashboard extends StatefulWidget {
  const ClientDashboard({super.key});

  @override
  State<ClientDashboard> createState() => _ClientDashboardState();
}

class _ClientDashboardState extends State<ClientDashboard> {
  int _currentIndex = 0; // Start with Home (Services) page

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<DemoAuthProvider>(context);
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        body: _buildCurrentPage(authProvider, isArabic),
        bottomNavigationBar: EnhancedBottomNavigationBar(
          key: const Key('client_bottom_nav'), // مفتاح للتحديد في Inspector
          currentIndex: _currentIndex,
          onTap: (index) {
            if (mounted) {
              setState(() {
                _currentIndex = index;
              });
            }
          },
          items: [
            EnhancedBottomNavItem(icon: Icons.home, label: AppLocalizations.of(context).home), // الصفحة الرئيسية أولاً
            EnhancedBottomNavItem(icon: Icons.work, label: isArabic ? 'طلباتي' : 'My Orders'),
            EnhancedBottomNavItem(icon: Icons.chat, label: isArabic ? 'الرسائل' : 'Messages'),
          ],
        ),
        floatingActionButton: _shouldShowFAB() ? _buildQuickRequestFAB(isArabic) : null,
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      ),
    );
  }

  Widget _buildCurrentPage(DemoAuthProvider authProvider, bool isArabic) {
    // Navigation order: Home (Services), Orders, Messages
    if (_currentIndex == 0) {
      return const ServicesScreen(); // Home (Services) - الصفحة الرئيسية
    } else if (_currentIndex == 1) {
      return const MyOrdersPage(); // Orders
    } else if (_currentIndex == 2) {
      return const ChatListScreen(); // Messages
    }

    return const ServicesScreen(); // Default to services
  }

  /// Check if FAB should be shown (hide when keyboard is visible)
  bool _shouldShowFAB() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    return keyboardHeight == 0; // Hide FAB when keyboard is visible
  }

  /// Enhanced Floating Action Button for Quick Request Creation
  Widget _buildQuickRequestFAB(bool isArabic) {
    return EnhancedFloatingActionButton(
      onPressed: () => _onQuickRequestTap(),
      icon: Icons.add,
      tooltip: isArabic ? 'إنشاء طلب جديد' : 'Create New Request',
      backgroundColor: ThemeProvider.primaryBlue,
      foregroundColor: Colors.white,
    );
  }

  /// Handle Quick Request FAB tap with haptic feedback
  void _onQuickRequestTap() {
    // Add haptic feedback for better user experience
    HapticFeedback.lightImpact();

    // Navigate to CreateRequestScreen
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => const CreateRequestScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

          return SlideTransition(position: animation.drive(tween), child: child);
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}
