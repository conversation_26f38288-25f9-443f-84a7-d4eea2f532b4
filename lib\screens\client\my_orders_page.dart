import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../utils/app_localizations.dart';
import '../../models/order_model.dart';
import '../../models/user_model.dart';
import '../../providers/language_provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../services/order_service.dart';
import '../chat/chat_screen.dart';
import 'order_detail_view.dart';

class MyOrdersPage extends StatefulWidget {
  const MyOrdersPage({super.key});

  @override
  State<MyOrdersPage> createState() => _MyOrdersPageState();
}

class _MyOrdersPageState extends State<MyOrdersPage> with TickerProviderStateMixin {
  late TabController _tabController;
  List<OrderModel> _orders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild to update tab colors
    });
    _ensureUserLoggedIn();
    _loadOrders();
  }

  void _ensureUserLoggedIn() {
    final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
    if (!authProvider.isAuthenticated) {
      authProvider.quickLogin(UserRole.client);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadOrders() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
      final userId = authProvider.user?.id;

      if (userId != null) {
        final orders = await OrderService.getOrders(clientId: userId);
        setState(() {
          _orders = orders;
          _isLoading = false;
        });
      } else {
        setState(() {
          _orders = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _orders = [];
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Directionality(
      textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
      child: Scaffold(
        body: Column(
          children: [
            // Status Filter Bar (No Header Background)
            SafeArea(
              top: true,
              bottom: false,
              child: Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Column(
                  children: [
                      // Tab Bar Section
                      // Enhanced Status Filter Bar (Matching Freelancer Offers Design)
                      Container(
                        padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color:
                                isDark
                                    ? ThemeProvider.darkCardBackground.withValues(alpha: 0.8)
                                    : Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              // Waiting Orders - انتظار
                              Expanded(
                                child: _buildEnhancedStatusTab(
                                  icon: Icons.hourglass_empty,
                                  label: isArabic ? 'انتظار' : 'Waiting',
                                  count: _getWaitingOrders().length,
                                  color: Colors.blue,
                                  isSelected: _tabController.index == 0,
                                  onTap: () => _tabController.animateTo(0),
                                ),
                              ),
                              // In Progress - قيد التنفيذ
                              Expanded(
                                child: _buildEnhancedStatusTab(
                                  icon: Icons.work_outline,
                                  label: isArabic ? 'قيد التنفيذ' : 'In Progress',
                                  count: _getInProgressOrders().length,
                                  color: Colors.purple,
                                  isSelected: _tabController.index == 1,
                                  onTap: () => _tabController.animateTo(1),
                                ),
                              ),
                              // Completed - مكتمل
                              Expanded(
                                child: _buildEnhancedStatusTab(
                                  icon: Icons.check_circle_outline,
                                  label: isArabic ? 'مكتمل' : 'Completed',
                                  count: _getCompletedOrders().length,
                                  color: Colors.green,
                                  isSelected: _tabController.index == 2,
                                  onTap: () => _tabController.animateTo(2),
                                ),
                              ),
                              // Canceled - ملغي
                              Expanded(
                                child: _buildEnhancedStatusTab(
                                  icon: Icons.cancel_outlined,
                                  label: isArabic ? 'ملغي' : 'Canceled',
                                  count: _getCanceledOrders().length,
                                  color: Colors.red,
                                  isSelected: _tabController.index == 3,
                                  onTap: () => _tabController.animateTo(3),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Content Section
            Expanded(
              child:
                  _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : TabBarView(
                        controller: _tabController,
                        children: [
                          _buildOrdersList(_getWaitingOrders(), isArabic, isDark, 'waiting'),
                          _buildOrdersList(_getInProgressOrders(), isArabic, isDark, 'inProgress'),
                          _buildOrdersList(_getCompletedOrders(), isArabic, isDark, 'completed'),
                          _buildOrdersList(_getCanceledOrders(), isArabic, isDark, 'canceled'),
                        ],
                      ),
                  ],
                ),
              ),
            ),

            // Content Section
            Expanded(
              child:
                  _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : TabBarView(
                        controller: _tabController,
                        children: [
                          _buildOrdersList(_getWaitingOrders(), isArabic, isDark, 'waiting'),
                          _buildOrdersList(_getInProgressOrders(), isArabic, isDark, 'inProgress'),
                          _buildOrdersList(_getCompletedOrders(), isArabic, isDark, 'completed'),
                          _buildOrdersList(_getCanceledOrders(), isArabic, isDark, 'canceled'),
                        ],
                      ),
            ),
          ],
        ),
      ),
    )
  }

  // Filter orders by status
  List<OrderModel> _getWaitingOrders() {
    return _orders
        .where((order) => order.status == OrderStatus.created || order.status == OrderStatus.paymentPending)
        .toList();
  }

  List<OrderModel> _getInProgressOrders() {
    return _orders
        .where(
          (order) =>
              order.status == OrderStatus.paymentConfirmed ||
              order.status == OrderStatus.inProgress ||
              order.status == OrderStatus.submitted ||
              order.status == OrderStatus.delivered,
        )
        .toList();
  }

  List<OrderModel> _getCompletedOrders() {
    return _orders.where((order) => order.status == OrderStatus.completed).toList();
  }

  List<OrderModel> _getCanceledOrders() {
    return _orders.where((order) => order.status == OrderStatus.cancelled).toList();
  }

  Widget _buildOrdersList(List<OrderModel> orders, bool isArabic, bool isDark, String category) {
    if (orders.isEmpty) {
      return _buildEmptyState(category, isArabic, isDark);
    }

    return RefreshIndicator(
      onRefresh: _loadOrders,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          return _buildOrderCard(order, isArabic, isDark);
        },
      ),
    );
  }

  Widget _buildEmptyState(String category, bool isArabic, bool isDark) {
    IconData icon;
    String message;
    Color iconColor;

    switch (category) {
      case 'waiting':
        icon = Icons.hourglass_empty;
        message = isArabic ? 'لا توجد طلبات في الانتظار' : 'No waiting orders';
        iconColor = Colors.orange;
        break;
      case 'inProgress':
        icon = Icons.work_outline;
        message = isArabic ? 'لا توجد طلبات قيد التنفيذ' : 'No orders in progress';
        iconColor = Colors.orange;
        break;
      case 'completed':
        icon = Icons.check_circle_outline;
        message = isArabic ? 'لا توجد طلبات مكتملة' : 'No completed orders';
        iconColor = Colors.green;
        break;
      case 'canceled':
        icon = Icons.cancel_outlined;
        message = isArabic ? 'لا توجد طلبات ملغية' : 'No canceled orders';
        iconColor = Colors.red;
        break;
      default:
        icon = Icons.inbox_outlined;
        message = isArabic ? 'لا توجد طلبات' : 'No orders';
        iconColor = Colors.grey;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: iconColor.withValues(alpha: 0.5)),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(OrderModel order, bool isArabic, bool isDark) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.08),
            blurRadius: 12,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: isDark ? Colors.grey[700]! : Colors.grey[200]!, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getOrderTitle(order, isArabic),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        isArabic ? 'طلب #${_safeSubstring(order.id, 8)}' : 'Order #${_safeSubstring(order.id, 8)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                _buildStatusBadge(order.status, isArabic),
              ],
            ),

            const SizedBox(height: 16),

            // Freelancer Info Row
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
                  child: const Icon(Icons.person, color: ThemeProvider.primaryBlue, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isArabic ? 'المستقل المكلف' : 'Assigned Freelancer',
                        style: TextStyle(
                          fontSize: 12,
                          color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'أحمد محمد', // Demo freelancer name
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Last Update Row
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  isArabic
                      ? 'آخر تحديث: ${_formatDateTime(order.updatedAt ?? order.createdAt, isArabic)}'
                      : 'Last updated: ${_formatDateTime(order.updatedAt ?? order.createdAt, isArabic)}',
                  style: TextStyle(fontSize: 12, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Action Buttons Row
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _openChat(order, isArabic),
                    icon: const Icon(Icons.chat_bubble_outline, size: 16),
                    label: Text(isArabic ? 'محادثة' : 'View Chat', style: const TextStyle(fontSize: 12)),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _openOrderDetails(order, isArabic),
                    icon: const Icon(Icons.visibility_outlined, size: 16),
                    label: Text(isArabic ? 'التفاصيل' : 'View Details', style: const TextStyle(fontSize: 12)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ThemeProvider.primaryBlue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper Methods
  Widget _buildStatusBadge(OrderStatus status, bool isArabic) {
    Color color;
    String text;
    IconData icon;

    switch (status) {
      case OrderStatus.created:
      case OrderStatus.paymentPending:
        color = Colors.orange;
        text = isArabic ? 'في الانتظار' : 'Waiting';
        icon = Icons.hourglass_empty;
        break;
      case OrderStatus.paymentConfirmed:
      case OrderStatus.inProgress:
        color = Colors.blue;
        text = isArabic ? 'قيد التنفيذ' : 'In Progress';
        icon = Icons.work_outline;
        break;
      case OrderStatus.submitted:
      case OrderStatus.delivered:
        color = Colors.teal;
        text = isArabic ? 'مُسلَّم' : 'Delivered';
        icon = Icons.check_circle_outline;
        break;
      case OrderStatus.completed:
        color = Colors.green;
        text = isArabic ? 'مكتمل' : 'Completed';
        icon = Icons.verified;
        break;
      case OrderStatus.cancelled:
        color = Colors.red;
        text = isArabic ? 'ملغي' : 'Canceled';
        icon = Icons.cancel_outlined;
        break;
      default:
        color = Colors.grey;
        text = isArabic ? 'غير محدد' : 'Unknown';
        icon = Icons.help_outline;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(text, style: TextStyle(color: color, fontWeight: FontWeight.bold, fontSize: 10)),
        ],
      ),
    );
  }

  String _getOrderTitle(OrderModel order, bool isArabic) {
    // Map order IDs to service titles for demo
    final serviceTitles = {
      'order_001': isArabic ? 'تصميم شعار احترافي للشركة' : 'Professional Company Logo Design',
      'order_002': isArabic ? 'تطوير تطبيق جوال بـ Flutter' : 'Flutter Mobile App Development',
      'order_003': isArabic ? 'ترجمة مقال علمي من الإنجليزية للعربية' : 'Scientific Article Translation EN-AR',
      'order_004': isArabic ? 'تحليل البيانات باستخدام SPSS' : 'Data Analysis using SPSS',
      'order_005': isArabic ? 'مراجعة وتدقيق نص أكاديمي' : 'Academic Text Review & Proofreading',
    };

    return serviceTitles[order.id] ?? (isArabic ? 'خدمة عامة' : 'General Service');
  }

  String _safeSubstring(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength);
  }

  String _formatDateTime(DateTime dateTime, bool isArabic) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return isArabic ? 'منذ ${difference.inDays} يوم' : '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return isArabic ? 'منذ ${difference.inHours} ساعة' : '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return isArabic ? 'منذ ${difference.inMinutes} دقيقة' : '${difference.inMinutes} minutes ago';
    } else {
      return isArabic ? 'الآن' : 'Just now';
    }
  }

  void _openChat(OrderModel order, bool isArabic) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ChatScreen(
              chatId: order.id,
              recipientName: isArabic ? 'المستقل' : 'Freelancer',
              requestTitle: isArabic ? 'طلب رقم ${order.id}' : 'Order #${order.id}',
              isAdminChat: false,
              orderId: order.id,
            ),
      ),
    );
  }

  void _openOrderDetails(OrderModel order, bool isArabic) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => OrderDetailView(order: order, isArabic: isArabic)));
  }

  Widget _buildEnhancedStatusTab({
    required IconData icon,
    required String label,
    required int count,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        margin: const EdgeInsets.symmetric(horizontal: 2),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          gradient:
              isSelected
                  ? LinearGradient(
                    colors: [color, color.withValues(alpha: 0.8)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
          color: isSelected ? null : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          boxShadow:
              isSelected
                  ? [BoxShadow(color: color.withValues(alpha: 0.3), blurRadius: 8, offset: const Offset(0, 2))]
                  : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 20, color: isSelected ? Colors.white : color.withValues(alpha: 0.7)),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected ? Colors.white.withValues(alpha: 0.2) : color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                count.toString(),
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: isSelected ? Colors.white : color),
              ),
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                fontSize: 11,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
                color: isSelected ? Colors.white : color.withValues(alpha: 0.9),
                letterSpacing: 0.3,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
