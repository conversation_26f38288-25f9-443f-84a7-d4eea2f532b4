import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../utils/app_localizations.dart';
import '../../models/order_model.dart';
import '../../models/service_request_model.dart';
import '../../models/user_model.dart';
import '../../providers/language_provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../services/order_service.dart';
import '../../services/request_service.dart';
import '../chat/chat_screen.dart';

enum OrderStatusFilter { waiting, active, completed, cancelled }

class AdvancedOrderPage extends StatefulWidget {
  const AdvancedOrderPage({super.key});

  @override
  State<AdvancedOrderPage> createState() => _AdvancedOrderPageState();
}

class _AdvancedOrderPageState extends State<AdvancedOrderPage> with TickerProviderStateMixin {
  late TabController _tabController;
  List<OrderModel> _orders = [];
  List<ServiceRequestModel> _pendingRequests = [];
  bool _isLoading = true;
  final OrderStatusFilter _selectedStatus = OrderStatusFilter.active;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _ensureUserLoggedIn();
    _loadOrders();
  }

  void _ensureUserLoggedIn() {
    final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
    if (!authProvider.isAuthenticated) {
      print('=== DEBUG: User not authenticated, quick login as client ===');
      authProvider.quickLogin(UserRole.client);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadOrders() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
      final userId = authProvider.user?.id;

      print('=== DEBUG: AdvancedOrderPage._loadOrders ===');
      print('User ID: $userId');
      print('User authenticated: ${authProvider.isAuthenticated}');

      if (userId != null) {
        final orders = await OrderService.getOrders(clientId: userId);
        final pendingRequests = await RequestService.getRequests(clientId: userId, status: RequestStatus.pending);

        print('Orders loaded: ${orders.length}');
        print('Pending requests loaded: ${pendingRequests.length}');

        setState(() {
          _orders = orders;
          _pendingRequests = pendingRequests;
          _isLoading = false;
        });
      } else {
        print('User ID is null, setting empty lists');
        setState(() {
          _orders = [];
          _pendingRequests = [];
          _isLoading = false;
        });
      }
      print('=== END DEBUG ===');
    } catch (e) {
      print('=== ERROR in _loadOrders: $e ===');
      setState(() {
        _orders = [];
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: isDark ? ThemeProvider.darkBackground : Colors.grey[50],
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
            child: TabBar(
              controller: _tabController,
              isScrollable: false,
              labelColor: ThemeProvider.primaryBlue,
              unselectedLabelColor: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
              indicatorColor: ThemeProvider.primaryBlue,
              indicatorWeight: 3,
              labelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
              unselectedLabelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.normal),
              tabs: [
                Tab(text: isArabic ? 'انتظار' : 'Waiting'),
                Tab(text: isArabic ? 'نشط' : 'Active'),
                Tab(text: isArabic ? 'مكتمل' : 'Completed'),
                Tab(text: isArabic ? 'ملغي' : 'Cancelled'),
              ],
            ),
          ),

          // Content
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : TabBarView(
                      controller: _tabController,
                      children: [
                        _buildPendingRequestsList(isArabic, isDark, l10n),
                        _buildOrdersList(
                          _getOrdersByStatus(OrderStatus.paymentPending, OrderStatus.inProgress),
                          isArabic,
                          isDark,
                          l10n,
                        ),
                        _buildOrdersList(
                          _getOrdersByStatus(OrderStatus.delivered, OrderStatus.completed),
                          isArabic,
                          isDark,
                          l10n,
                        ),
                        _buildOrdersList(_getOrdersByStatus(OrderStatus.cancelled), isArabic, isDark, l10n),
                      ],
                    ),
          ),
        ],
      ),
    );
  }

  List<OrderModel> _getOrdersByStatus(OrderStatus status1, [OrderStatus? status2]) {
    return _orders.where((order) => order.status == status1 || (status2 != null && order.status == status2)).toList();
  }

  Widget _buildEnhancedFilterBar(bool isArabic, bool isDark) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)),
          boxShadow: [
            BoxShadow(color: Colors.black.withValues(alpha: 0.05), blurRadius: 8, offset: const Offset(0, 2)),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          isScrollable: false,
          labelColor: ThemeProvider.primaryBlue,
          unselectedLabelColor: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
          indicatorColor: ThemeProvider.primaryBlue,
          indicatorWeight: 3,
          indicatorSize: TabBarIndicatorSize.tab,
          indicator: BoxDecoration(
            color: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          labelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
          unselectedLabelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.normal),
          tabs: [
            Tab(text: isArabic ? 'انتظار' : 'Waiting'),
            Tab(text: isArabic ? 'نشط' : 'Active'),
            Tab(text: isArabic ? 'مكتمل' : 'Completed'),
            Tab(text: isArabic ? 'ملغي' : 'Cancelled'),
          ],
        ),
      ),
    );
  }

  // Build Orders List
  Widget _buildOrdersList(List<OrderModel> orders, bool isArabic, bool isDark, AppLocalizations l10n) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _getEmptyStateText(_selectedStatus, isArabic),
              style: TextStyle(fontSize: 18, color: Colors.grey[600], fontWeight: FontWeight.w500),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadOrders,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          return _buildEnhancedOrderCard(order, isArabic, isDark, l10n);
        },
      ),
    );
  }

  // Build Pending Requests List
  Widget _buildPendingRequestsList(bool isArabic, bool isDark, AppLocalizations l10n) {
    if (_pendingRequests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              isArabic ? 'لا توجد طلبات في الانتظار' : 'No pending requests',
              style: TextStyle(fontSize: 18, color: Colors.grey[600], fontWeight: FontWeight.w500),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadOrders,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _pendingRequests.length,
        itemBuilder: (context, index) {
          final request = _pendingRequests[index];
          return _buildPendingRequestCard(request, isArabic, isDark);
        },
      ),
    );
  }

  String _getEmptyStateText(OrderStatusFilter status, bool isArabic) {
    switch (status) {
      case OrderStatusFilter.waiting:
        return isArabic ? 'لا توجد طلبات في الانتظار' : 'No pending requests';
      case OrderStatusFilter.active:
        return isArabic ? 'لا توجد طلبات نشطة' : 'No active orders';
      case OrderStatusFilter.completed:
        return isArabic ? 'لا توجد طلبات مكتملة' : 'No completed orders';
      case OrderStatusFilter.cancelled:
        return isArabic ? 'لا توجد طلبات ملغية' : 'No cancelled orders';
    }
  }

  String _safeSubstring(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength);
  }

  String _getServiceTitle(String orderId, bool isArabic) {
    // Map order IDs to service titles
    final serviceTitles = {
      'order_001': isArabic ? 'تصميم شعار احترافي للشركة' : 'Professional Company Logo Design',
      'order_002': isArabic ? 'تطوير تطبيق جوال بـ Flutter' : 'Flutter Mobile App Development',
      'order_003': isArabic ? 'ترجمة مقال علمي من الإنجليزية للعربية' : 'Scientific Article Translation EN-AR',
      'order_004': isArabic ? 'تحليل البيانات باستخدام SPSS' : 'Data Analysis using SPSS',
      'order_005': isArabic ? 'مراجعة وتدقيق نص أكاديمي' : 'Academic Text Review & Proofreading',
    };

    return serviceTitles[orderId] ?? (isArabic ? 'خدمة عامة' : 'General Service');
  }

  // Build Chat Button
  Widget _buildChatButton(OrderModel order, bool isArabic, bool isDark) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _openChat(order, isArabic),
        icon: const Icon(Icons.chat_bubble_outline, size: 18),
        label: Text(
          isArabic ? 'محادثة المستقل' : 'Chat with Freelancer',
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: ThemeProvider.primaryBlue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          elevation: 2,
        ),
      ),
    );
  }

  void _openChat(OrderModel order, bool isArabic) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ChatScreen(
              chatId: order.id,
              recipientName: isArabic ? 'المستقل' : 'Freelancer',
              requestTitle: isArabic ? 'طلب رقم ${order.id}' : 'Order #${order.id}',
              isAdminChat: false,
              orderId: order.id,
            ),
      ),
    );
  }

  // Build Enhanced Order Card
  Widget _buildEnhancedOrderCard(OrderModel order, bool isArabic, bool isDark, AppLocalizations l10n) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.08),
            blurRadius: 12,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: isDark ? Colors.grey[700]! : Colors.grey[200]!, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getServiceTitle(order.id, isArabic),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        isArabic ? 'طلب #${_safeSubstring(order.id, 8)}' : 'Order #${_safeSubstring(order.id, 8)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${order.amount.toStringAsFixed(0)} ريال',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: ThemeProvider.primaryBlue,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                _buildStatusBadge(order.status, isArabic),
              ],
            ),

            const SizedBox(height: 16),

            // Freelancer Info
            _buildFreelancerInfo(order, isArabic, isDark),

            const SizedBox(height: 16),

            // Last Updated
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  isArabic
                      ? 'آخر تحديث: ${_formatDateTime(order.updatedAt ?? order.createdAt, isArabic)}'
                      : 'Last updated: ${_formatDateTime(order.updatedAt ?? order.createdAt, isArabic)}',
                  style: TextStyle(fontSize: 12, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Chat Button
            _buildChatButton(order, isArabic, isDark),
          ],
        ),
      ),
    );
  }

  // Build Status Badge
  Widget _buildStatusBadge(OrderStatus status, bool isArabic) {
    Color color;
    String text;
    IconData icon;

    switch (status) {
      case OrderStatus.created:
      case OrderStatus.paymentPending:
        color = ThemeProvider.warningOrange;
        text = isArabic ? 'في انتظار الدفع' : 'Payment Pending';
        icon = Icons.payment;
        break;
      case OrderStatus.paymentConfirmed:
      case OrderStatus.inProgress:
        color = ThemeProvider.primaryBlue;
        text = isArabic ? 'قيد التنفيذ' : 'In Progress';
        icon = Icons.work_outline;
        break;
      case OrderStatus.submitted:
      case OrderStatus.delivered:
        color = Colors.teal;
        text = isArabic ? 'مُسلَّم' : 'Delivered';
        icon = Icons.check_circle_outline;
        break;
      case OrderStatus.completed:
        color = ThemeProvider.successGreen;
        text = isArabic ? 'مكتمل' : 'Completed';
        icon = Icons.verified;
        break;
      case OrderStatus.cancelled:
        color = Colors.red;
        text = isArabic ? 'ملغي' : 'Cancelled';
        icon = Icons.cancel_outlined;
        break;
      default:
        color = Colors.grey;
        text = isArabic ? 'جديد' : 'New';
        icon = Icons.fiber_new;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.12),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(text, style: TextStyle(color: color, fontSize: 11, fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }

  // Build Freelancer Info
  Widget _buildFreelancerInfo(OrderModel order, bool isArabic, bool isDark) {
    return FutureBuilder<UserModel?>(
      future: _getFreelancerInfo(order.freelancerId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox(height: 20, child: Center(child: CircularProgressIndicator()));
        }

        final freelancer = snapshot.data;
        if (freelancer == null) {
          return Text(
            isArabic ? 'معلومات المستقل غير متوفرة' : 'Freelancer info unavailable',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          );
        }

        return Row(
          children: [
            CircleAvatar(
              radius: 16,
              backgroundColor: ThemeProvider.primaryBlue,
              child: Text(
                freelancer.fullName?.isNotEmpty == true ? freelancer.fullName![0] : 'F',
                style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    freelancer.fullName ?? (isArabic ? 'مستقل' : 'Freelancer'),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                    ),
                  ),
                  if (freelancer.rating != null)
                    Row(
                      children: [
                        const Icon(Icons.star, size: 14, color: Colors.amber),
                        const SizedBox(width: 4),
                        Text(
                          '${freelancer.rating!.toStringAsFixed(1)} (${freelancer.completedJobs ?? 0})',
                          style: TextStyle(
                            fontSize: 12,
                            color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  // Build Pending Request Card
  Widget _buildPendingRequestCard(ServiceRequestModel request, bool isArabic, bool isDark) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              request.title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              request.description,
              style: TextStyle(fontSize: 14, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${request.budget?.toStringAsFixed(0) ?? '0'} ريال',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: ThemeProvider.successGreen),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    isArabic ? 'انتظار' : 'Waiting',
                    style: const TextStyle(color: Colors.orange, fontWeight: FontWeight.bold, fontSize: 11),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime, bool isArabic) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 7) {
      return isArabic
          ? '${dateTime.day}/${dateTime.month}/${dateTime.year}'
          : '${dateTime.month}/${dateTime.day}/${dateTime.year}';
    } else if (difference.inDays > 0) {
      return isArabic ? 'منذ ${difference.inDays} يوم' : '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return isArabic ? 'منذ ${difference.inHours} ساعة' : '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return isArabic ? 'منذ ${difference.inMinutes} دقيقة' : '${difference.inMinutes}m ago';
    }
    return isArabic ? 'الآن' : 'Now';
  }

  /// Get freelancer information by ID
  Future<UserModel?> _getFreelancerInfo(String freelancerId) async {
    try {
      // For demo purposes, create a demo freelancer based on the freelancerId
      // In production, this would fetch from the database
      return UserModel(
        id: freelancerId,
        email: '<EMAIL>',
        fullName: 'أحمد محمد',
        role: UserRole.freelancer,
        avatarUrl: 'https://via.placeholder.com/150',
        rating: 4.8,
        completedJobs: 156,
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
        isVerified: true,
      );
    } catch (e) {
      return null;
    }
  }
}
