import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/order_model.dart';
import '../../providers/language_provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../services/order_service.dart';
import '../../utils/app_localizations.dart';
import '../chat/chat_screen.dart';

class MyOrdersScreen extends StatefulWidget {
  const MyOrdersScreen({super.key});

  @override
  State<MyOrdersScreen> createState() => _MyOrdersScreenState();
}

class _MyOrdersScreenState extends State<MyOrdersScreen> {
  List<OrderModel> _orders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  Future<void> _loadOrders() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
      final userId = authProvider.user?.id;

      if (userId != null) {
        final orders = await OrderService.getOrders(clientId: userId);
        setState(() {
          _orders = orders;
          _isLoading = false;
        });
      } else {
        setState(() {
          _orders = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _orders = [];
        _isLoading = false;
      });
    }
  }

  List<OrderModel> _getOrdersByStatus(OrderStatus status) {
    return _orders.where((order) => order.status == status).toList();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    // Handle null localization for testing
    AppLocalizations? l10n;
    try {
      l10n = AppLocalizations.of(context);
    } catch (e) {
      // Localization not available in test environment
      l10n = null;
    }

    if (l10n == null) {
      return const Scaffold(body: Center(child: Text('Localization not available')));
    }

    return DefaultTabController(
      length: 4,
      child: Directionality(
        textDirection: languageProvider.textDirection,
        child: Scaffold(
          body: Column(
            children: [
              // Header matching app's consistent design system
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                    ],
                  ),
                ),
                child: SafeArea(
                  top: false,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 20),
                    child: Column(
                      children: [
                        // Title Section - centered
                        Container(
                          height: 56,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Center(
                            child: Text(
                              languageProvider.isArabic ? 'طلباتي' : 'My Orders',
                              style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white, fontSize: 20),
                            ),
                          ),
                        ),
                        // Tab Bar Section
                        TabBar(
                          labelColor: Colors.white,
                          unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
                          indicatorColor: Colors.white,
                          indicatorWeight: 3,
                          labelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
                          unselectedLabelStyle: const TextStyle(fontSize: 11, fontWeight: FontWeight.w500),
                          tabs: [
                            Tab(
                              icon: const Icon(Icons.assignment, size: 20),
                              text: languageProvider.isArabic ? 'نشطة' : 'Active',
                            ),
                            Tab(
                              icon: const Icon(Icons.upload, size: 20),
                              text: languageProvider.isArabic ? 'مُسلَّمة' : 'Delivered',
                            ),
                            Tab(
                              icon: const Icon(Icons.check_circle, size: 20),
                              text: languageProvider.isArabic ? 'مكتملة' : 'Completed',
                            ),
                            Tab(
                              icon: const Icon(Icons.cancel, size: 20),
                              text: languageProvider.isArabic ? 'ملغية' : 'Cancelled',
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // Content Section
              Expanded(
                child:
                    _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : TabBarView(
                          children: [
                            _buildActiveOrdersList(l10n, languageProvider),
                            _buildOrdersList(OrderStatus.delivered, l10n, languageProvider),
                            _buildOrdersList(OrderStatus.completed, l10n, languageProvider),
                            _buildOrdersList(OrderStatus.cancelled, l10n, languageProvider),
                          ],
                        ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActiveOrdersList(AppLocalizations l10n, LanguageProvider languageProvider) {
    final activeOrders =
        _orders
            .where((order) => order.status != OrderStatus.completed && order.status != OrderStatus.cancelled)
            .toList();

    if (activeOrders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              languageProvider.isArabic ? 'لا توجد طلبات نشطة' : 'No active orders',
              style: TextStyle(fontSize: 18, color: Colors.grey[600], fontWeight: FontWeight.w500),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: activeOrders.length,
      itemBuilder: (context, index) {
        final order = activeOrders[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            title: Text('Order #${order.id}'),
            subtitle: Text('Request: ${order.requestId}'),
            trailing: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(_getStatusIcon(order.status)),
                Text(_getStatusText(order.status, languageProvider.isArabic)),
              ],
            ),
            onTap: () {
              print('=== DEBUG: My Orders - Order card tapped ===');
              print('Order ID: ${order.id}');
              print('Order Status: ${order.status}');
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => ChatScreen(
                        chatId: 'order_${order.id}',
                        recipientName: 'Freelancer',
                        requestTitle: 'Order #${order.id}',
                        orderId: order.id, // Pass the order ID
                      ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildOrdersList(OrderStatus status, AppLocalizations l10n, LanguageProvider languageProvider) {
    final orders = _getOrdersByStatus(status);

    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _getEmptyStateText(status, languageProvider.isArabic),
              style: TextStyle(fontSize: 18, color: Colors.grey[600], fontWeight: FontWeight.w500),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            title: Text('Order #${order.id}'),
            subtitle: Text('Request: ${order.requestId}'),
            trailing: Column(
              mainAxisSize: MainAxisSize.min,
              children: [Icon(_getStatusIcon(status)), Text(_getStatusText(status, languageProvider.isArabic))],
            ),
            onTap: () {
              print('=== DEBUG: My Orders - Order card tapped ===');
              print('Order ID: ${order.id}');
              print('Order Status: ${order.status}');
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => ChatScreen(
                        chatId: 'order_${order.id}',
                        recipientName: 'Freelancer',
                        requestTitle: 'Order #${order.id}',
                        orderId: order.id, // Pass the order ID
                      ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  IconData _getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.delivered:
        return Icons.check_circle;
      case OrderStatus.completed:
        return Icons.check_circle;
      case OrderStatus.cancelled:
        return Icons.cancel;
      case OrderStatus.inProgress:
        return Icons.work;
      case OrderStatus.editing:
        return Icons.edit;
      default:
        return Icons.assignment;
    }
  }

  String _getStatusText(OrderStatus status, bool isArabic) {
    switch (status) {
      case OrderStatus.created:
        return isArabic ? 'تم الإنشاء' : 'Created';

      case OrderStatus.paymentConfirmed:
        return isArabic ? 'تم تأكيد الدفع' : 'Payment Confirmed';
      case OrderStatus.inProgress:
        return isArabic ? 'قيد التنفيذ' : 'In Progress';
      case OrderStatus.submitted:
        return isArabic ? 'تم التسليم للمراجعة' : 'Submitted for Review';
      case OrderStatus.delivered:
        return isArabic ? 'مُسلَّم' : 'Delivered';
      case OrderStatus.editing:
        return isArabic ? 'قيد التعديل' : 'Editing';
      case OrderStatus.completed:
        return isArabic ? 'مكتمل' : 'Completed';
      case OrderStatus.cancelled:
        return isArabic ? 'ملغي' : 'Cancelled';
      case OrderStatus.paymentPending:
        return isArabic ? 'قيد التنفيذ' : 'In Progress';
    }
  }

  String _getEmptyStateText(OrderStatus status, bool isArabic) {
    switch (status) {
      case OrderStatus.delivered:
        return isArabic ? 'لا توجد طلبات مُسلَّمة' : 'No delivered orders';
      case OrderStatus.completed:
        return isArabic ? 'لا توجد طلبات مكتملة' : 'No completed orders';
      case OrderStatus.cancelled:
        return isArabic ? 'لا توجد طلبات ملغية' : 'No cancelled orders';
      default:
        return isArabic ? 'لا توجد طلبات' : 'No orders';
    }
  }
}
